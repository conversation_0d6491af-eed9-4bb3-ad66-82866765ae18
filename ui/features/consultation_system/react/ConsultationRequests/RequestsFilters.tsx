import React, { useState } from 'react'
import { View } from '@instructure/ui-view'
import { Flex } from '@instructure/ui-flex'
import { Button } from '@instructure/ui-buttons'
import { TextInput } from '@instructure/ui-text-input'
import { Select } from '@instructure/ui-select'
import { Grid } from '@instructure/ui-grid'
import { IconSearchLine, IconXLine } from '@instructure/ui-icons'
import type { ConsultationFilters } from '../types'

interface RequestsFiltersProps {
  filters: ConsultationFilters
  concernTypes: string[]
  statuses: string[]
  userRole: 'student' | 'faculty'
  onFiltersChange: (filters: ConsultationFilters) => void
  loading: boolean
}

const RequestsFilters: React.FC<RequestsFiltersProps> = ({
  filters,
  concernTypes,
  statuses,
  userRole,
  onFiltersChange,
  loading
}) => {
  const [localFilters, setLocalFilters] = useState<ConsultationFilters>(filters)

  // State for Status Select
  const [statusInputValue, setStatusInputValue] = useState(() => {
    if (filters.status) {
      const foundStatus = statuses.find(s => s === filters.status)
      return foundStatus ? foundStatus.charAt(0).toUpperCase() + foundStatus.slice(1) : 'All Statuses'
    }
    return 'All Statuses'
  })
  const [isShowingStatusOptions, setIsShowingStatusOptions] = useState(false)

  // State for Concern Type Select
  const [concernTypeInputValue, setConcernTypeInputValue] = useState(
    filters.concern_type || 'All Types'
  )
  const [isShowingConcernTypeOptions, setIsShowingConcernTypeOptions] = useState(false)

  const handleFilterChange = (key: keyof ConsultationFilters, value: string) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  // Status Select handlers
  const handleStatusInputChange = () => {
    // For filter selects, we don't need input change handling
  }

  const handleShowStatusOptions = () => {
    setIsShowingStatusOptions(true)
  }

  const handleHideStatusOptions = () => {
    setIsShowingStatusOptions(false)
  }

  const handleSelectStatus = (_e: any, { id }: { id?: string }) => {
    const selectedStatus = id ? statuses.find(s => s === id) || '' : ''
    setStatusInputValue(selectedStatus ? selectedStatus.charAt(0).toUpperCase() + selectedStatus.slice(1) : 'All Statuses')
    handleFilterChange('status', selectedStatus)
    setIsShowingStatusOptions(false)
  }

  // Concern Type Select handlers
  const handleConcernTypeInputChange = () => {
    // For filter selects, we don't need input change handling
  }

  const handleShowConcernTypeOptions = () => {
    setIsShowingConcernTypeOptions(true)
  }

  const handleHideConcernTypeOptions = () => {
    setIsShowingConcernTypeOptions(false)
  }

  const handleSelectConcernType = (_e: any, { id }: { id?: string }) => {
    const selectedType = id ? concernTypes.find(t => t === id) || '' : ''
    setConcernTypeInputValue(selectedType || 'All Types')
    handleFilterChange('concern_type', selectedType)
    setIsShowingConcernTypeOptions(false)
  }



  const handleApplyFilters = () => {
    onFiltersChange(localFilters)
  }

  const handleClearFilters = () => {
    const clearedFilters = {}
    setLocalFilters(clearedFilters)
    onFiltersChange(clearedFilters)
  }

  const hasActiveFilters = Object.values(localFilters).some(value => value && value !== '')

  console.log('REQUEST FILTERS concernTypes', concernTypes)

  return (
    <View as="div" background="secondary" padding="medium" borderRadius="medium" margin="0 0 medium 0">
      <Grid>
        <Grid.Row>
          <Grid.Col width={3}>
            <Select
              renderLabel="Status"
              placeholder="All statuses"
              inputValue={statusInputValue || (localFilters.status ? (statuses.find(s => s === localFilters.status) || '').charAt(0).toUpperCase() + (statuses.find(s => s === localFilters.status) || '').slice(1) : 'All Statuses')}
              isShowingOptions={isShowingStatusOptions}
              onInputChange={handleStatusInputChange}
              onRequestShowOptions={handleShowStatusOptions}
              onRequestHideOptions={handleHideStatusOptions}
              onRequestSelectOption={handleSelectStatus}
              assistiveText="Use arrow keys to navigate options"
            >
              <Select.Option key="all-statuses" id="all-statuses" value="">
                All Statuses
              </Select.Option>
              {statuses.map(status => (
                <Select.Option key={status} id={status} value={status}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </Select.Option>
              ))}
            </Select>
          </Grid.Col>
          
          <Grid.Col width={3}>
            <Select
              renderLabel="Concern Type"
              placeholder="All types"
              inputValue={concernTypeInputValue || (localFilters.concern_type || 'All Types')}
              isShowingOptions={isShowingConcernTypeOptions}
              onInputChange={handleConcernTypeInputChange}
              onRequestShowOptions={handleShowConcernTypeOptions}
              onRequestHideOptions={handleHideConcernTypeOptions}
              onRequestSelectOption={handleSelectConcernType}
              assistiveText="Use arrow keys to navigate options"
            >
              <Select.Option key="all-types" id="all-types" value="">
                All Types
              </Select.Option>
              {concernTypes.map((type) => (
                <Select.Option key={type} id={type} value={type}>
                  {type}
                </Select.Option>
              ))}
            </Select>
          </Grid.Col>
          
          <Grid.Col width={3}>
            <TextInput
              renderLabel={userRole === 'student' ? 'Search Faculty' : 'Search Students'}
              placeholder={userRole === 'student' ? 'Faculty name' : 'Student name or ID'}
              value={localFilters.student_search || ''}
              onChange={(e) => handleFilterChange('student_search', e.target.value)}
            />
          </Grid.Col>
          
          <Grid.Col width={3}>
            <Flex gap="small" alignItems="end" height="100%">
              <Flex.Item>
                <Button
                  color="primary"
                  renderIcon={() => <IconSearchLine />}
                  onClick={handleApplyFilters}
                  disabled={loading}
                >
                  Search
                </Button>
              </Flex.Item>
              {hasActiveFilters && (
                <Flex.Item>
                  <Button
                    renderIcon={() => <IconXLine />}
                    onClick={handleClearFilters}
                    disabled={loading}
                  >
                    Clear
                  </Button>
                </Flex.Item>
              )}
            </Flex>
          </Grid.Col>
        </Grid.Row>
        
        <Grid.Row>
          <Grid.Col width={3}>
            <View as="div">
              <label htmlFor="requests-start-date-input" style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500' }}>
                Start Date
              </label>
              <input
                id="requests-start-date-input"
                type="date"
                value={localFilters.start_date || ''}
                onChange={(e) => handleFilterChange('start_date', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  border: '1px solid #C7CDD1',
                  borderRadius: '0.25rem',
                  fontSize: '1rem',
                  backgroundColor: '#FFFFFF'
                }}
              />
            </View>
          </Grid.Col>

          <Grid.Col width={3}>
            <View as="div">
              <label htmlFor="requests-end-date-input" style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.875rem', fontWeight: '500' }}>
                End Date
              </label>
              <input
                id="requests-end-date-input"
                type="date"
                value={localFilters.end_date || ''}
                onChange={(e) => handleFilterChange('end_date', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  border: '1px solid #C7CDD1',
                  borderRadius: '0.25rem',
                  fontSize: '1rem',
                  backgroundColor: '#FFFFFF'
                }}
              />
            </View>
          </Grid.Col>
          
          <Grid.Col width={6}>
            <View as="div" padding="small 0 0 0">
              <Flex gap="medium" alignItems="center">
                <Flex.Item>
                  <label>
                    <input
                      type="checkbox"
                      checked={localFilters.content_search === 'urgent'}
                      onChange={(e) => handleFilterChange('content_search', e.target.checked ? 'urgent' : '')}
                    />
                    <span style={{ marginLeft: '0.5rem' }}>Urgent Requests</span>
                  </label>
                </Flex.Item>
                <Flex.Item>
                  <label>
                    <input
                      type="checkbox"
                      checked={localFilters.start_date === 'this_week'}
                      onChange={(e) => handleFilterChange('start_date', e.target.checked ? 'this_week' : '')}
                    />
                    <span style={{ marginLeft: '0.5rem' }}>Upcoming This Week</span>
                  </label>
                </Flex.Item>
              </Flex>
            </View>
          </Grid.Col>
        </Grid.Row>
      </Grid>
    </View>
  )
}

export default RequestsFilters
